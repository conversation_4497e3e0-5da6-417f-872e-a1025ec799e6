import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:novel_app/controllers/api_config_controller.dart';
import 'package:novel_app/controllers/knowledge_base_controller.dart';
import 'package:novel_app/controllers/writing_style_package_controller.dart';
import 'package:novel_app/services/character_card_service.dart';
import 'package:novel_app/models/novel.dart';
import 'package:novel_app/models/character_card.dart';
import 'package:novel_app/models/writing_style_package.dart';
import 'package:novel_app/models/knowledge_document.dart';

/// 半人马写作面板组件
/// 提供AI辅助写作的各种配置选项和生成功能
class CentaurWritingPanel extends StatefulWidget {
  final Novel novel;
  final int currentChapterIndex;
  final TextEditingController promptController;
  final TextEditingController plotDevelopmentController;
  final bool isGenerating;
  final VoidCallback onGenerate;
  final Function(Set<String>) onKnowledgeSelectionChanged;
  final Function(Set<String>) onCharacterSelectionChanged;
  final Function(Set<int>) onContextSelectionChanged;
  final Function(String?) onWritingStyleChanged;
  final Function(bool) onAllowCreateCharactersChanged;

  // 添加初始值参数
  final Set<String> initialSelectedKnowledgeIds;
  final Set<String> initialSelectedCharacterIds;
  final Set<int> initialSelectedContextChapters;
  final String? initialSelectedWritingStyleId;
  final bool initialAllowCreateNewCharacters;

  const CentaurWritingPanel({
    super.key,
    required this.novel,
    required this.currentChapterIndex,
    required this.promptController,
    required this.plotDevelopmentController,
    required this.isGenerating,
    required this.onGenerate,
    required this.onKnowledgeSelectionChanged,
    required this.onCharacterSelectionChanged,
    required this.onContextSelectionChanged,
    required this.onWritingStyleChanged,
    required this.onAllowCreateCharactersChanged,
    this.initialSelectedKnowledgeIds = const {},
    this.initialSelectedCharacterIds = const {},
    this.initialSelectedContextChapters = const {},
    this.initialSelectedWritingStyleId,
    this.initialAllowCreateNewCharacters = true,
  });

  @override
  State<CentaurWritingPanel> createState() => _CentaurWritingPanelState();
}

class _CentaurWritingPanelState extends State<CentaurWritingPanel> {
  final _selectedKnowledgeIds = <String>{};
  final _selectedCharacterIds = <String>{};
  final _selectedContextChapters = <int>{};
  String? _selectedWritingStyleId;
  bool _allowCreateNewCharacters = true;

  @override
  void initState() {
    super.initState();
    // 初始化选中的值
    _selectedKnowledgeIds.addAll(widget.initialSelectedKnowledgeIds);
    _selectedCharacterIds.addAll(widget.initialSelectedCharacterIds);
    _selectedContextChapters.addAll(widget.initialSelectedContextChapters);
    _selectedWritingStyleId = widget.initialSelectedWritingStyleId;
    _allowCreateNewCharacters = widget.initialAllowCreateNewCharacters;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Theme.of(context).scaffoldBackgroundColor,
      child: Column(
        children: [
          // 标题栏
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              border: Border(
                bottom: BorderSide(
                  color: Theme.of(context).dividerColor,
                ),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.auto_awesome,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  '半人马写作',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ],
            ),
          ),
          // 内容区域
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildModelSelector(),
                  const SizedBox(height: 16),
                  _buildKnowledgeBaseSelector(),
                  const SizedBox(height: 16),
                  _buildWritingStyleSelector(),
                  const SizedBox(height: 16),
                  _buildCharacterSelector(),
                  const SizedBox(height: 16),
                  _buildContextSelector(),
                  const SizedBox(height: 16),
                  _buildCharacterCreationToggle(),
                  const SizedBox(height: 16),
                  _buildPromptInput(),
                  const SizedBox(height: 16),
                  _buildPlotDevelopmentInput(),
                  const SizedBox(height: 16),
                  _buildGenerateButton(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建模型选择器
  Widget _buildModelSelector() {
    final apiController = Get.find<ApiConfigController>();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '模型选择',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Obx(() => InkWell(
          onTap: () => _showModelSelectionDialog(),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
            decoration: BoxDecoration(
              border: Border.all(color: Theme.of(context).dividerColor),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.smart_toy,
                  size: 16,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        apiController.selectedModelId.value,
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        _getModelDescription(apiController),
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.arrow_drop_down,
                      color: Theme.of(context).primaryColor,
                    ),
                    const SizedBox(width: 4),
                    IconButton(
                      icon: const Icon(Icons.settings, size: 16),
                      onPressed: () => Get.toNamed('/settings'),
                      tooltip: '模型设置',
                    ),
                  ],
                ),
              ],
            ),
          ),
        )),
      ],
    );
  }

  /// 获取模型描述信息
  String _getModelDescription(ApiConfigController apiController) {
    try {
      final currentModel = apiController.getCurrentModel();
      return '${currentModel.model} • ${currentModel.apiFormat}';
    } catch (e) {
      return '未配置模型';
    }
  }

  /// 显示模型选择弹窗
  void _showModelSelectionDialog() {
    final apiController = Get.find<ApiConfigController>();

    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.8,
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.6,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 标题栏
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primaryContainer,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        '选择模型',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onPrimaryContainer,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => Get.back(),
                      icon: Icon(
                        Icons.close,
                        color: Theme.of(context).colorScheme.onPrimaryContainer,
                      ),
                    ),
                  ],
                ),
              ),

              // 模型列表
              Flexible(
                child: Obx(() => apiController.models.isEmpty
                    ? const Padding(
                        padding: EdgeInsets.all(32),
                        child: Column(
                          children: [
                            Icon(Icons.warning, size: 48, color: Colors.orange),
                            SizedBox(height: 16),
                            Text(
                              '暂无可用模型',
                              style: TextStyle(fontSize: 16),
                              textAlign: TextAlign.center,
                            ),
                            SizedBox(height: 8),
                            Text(
                              '请先在设置页面配置模型',
                              style: TextStyle(fontSize: 14, color: Colors.grey),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      )
                    : ListView.builder(
                        shrinkWrap: true,
                        itemCount: apiController.models.length,
                        itemBuilder: (context, index) {
                          final model = apiController.models[index];
                          final isSelected = apiController.selectedModelId.value == model.name;

                          return ListTile(
                            leading: Icon(
                              Icons.smart_toy,
                              color: isSelected
                                  ? Theme.of(context).primaryColor
                                  : Colors.grey,
                            ),
                            title: Text(
                              model.name,
                              style: TextStyle(
                                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                              ),
                            ),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text('${model.model} • ${model.apiFormat}'),
                                const SizedBox(height: 2),
                                Row(
                                  children: [
                                    // 配置状态指示器
                                    Container(
                                      width: 8,
                                      height: 8,
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        color: model.apiKey.isNotEmpty
                                            ? Colors.green
                                            : Colors.orange,
                                      ),
                                    ),
                                    const SizedBox(width: 6),
                                    Text(
                                      model.apiKey.isNotEmpty ? '已配置' : '未配置API密钥',
                                      style: TextStyle(
                                        color: model.apiKey.isNotEmpty
                                            ? Colors.green.shade700
                                            : Colors.orange.shade700,
                                        fontSize: 12,
                                      ),
                                    ),
                                    if (model.maxTokens > 0) ...[
                                      const SizedBox(width: 8),
                                      Text(
                                        '• ${model.maxTokens} tokens',
                                        style: TextStyle(
                                          color: Colors.grey.shade600,
                                          fontSize: 12,
                                        ),
                                      ),
                                    ],
                                  ],
                                ),
                              ],
                            ),
                            trailing: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                if (model.apiKey.isEmpty)
                                  IconButton(
                                    icon: const Icon(Icons.edit, size: 16),
                                    onPressed: () => _showQuickConfigDialog(model),
                                    tooltip: '快速配置',
                                  ),
                                if (isSelected)
                                  Icon(Icons.check, color: Theme.of(context).primaryColor),
                              ],
                            ),
                            selected: isSelected,
                            onTap: () {
                              if (model.apiKey.isEmpty) {
                                _showQuickConfigDialog(model);
                              } else {
                                apiController.updateSelectedModel(model.name);
                                Get.back();
                                Get.snackbar(
                                  '模型已切换',
                                  '当前模型：${model.name}',
                                  backgroundColor: Colors.green.withOpacity(0.1),
                                  duration: const Duration(seconds: 2),
                                );
                              }
                            },
                          );
                        },
                      )),
              ),

              // 底部按钮
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    TextButton.icon(
                      onPressed: () {
                        Get.back();
                        Get.toNamed('/settings');
                      },
                      icon: const Icon(Icons.settings),
                      label: const Text('模型设置'),
                    ),
                    TextButton(
                      onPressed: () => Get.back(),
                      child: const Text('关闭'),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 显示快速配置弹窗
  void _showQuickConfigDialog(ModelConfig model) {
    final apiKeyController = TextEditingController(text: model.apiKey);
    final apiController = Get.find<ApiConfigController>();

    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.8,
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题
              Row(
                children: [
                  Icon(
                    Icons.settings,
                    color: Theme.of(context).primaryColor,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '快速配置 ${model.name}',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Get.back(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // 模型信息
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '模型信息',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text('模型名称: ${model.model}'),
                    Text('API格式: ${model.apiFormat}'),
                    Text('API地址: ${model.apiUrl}'),
                  ],
                ),
              ),
              const SizedBox(height: 16),

              // API密钥输入
              TextField(
                controller: apiKeyController,
                decoration: const InputDecoration(
                  labelText: 'API密钥',
                  hintText: '请输入API密钥',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.key),
                ),
                obscureText: true,
              ),
              const SizedBox(height: 16),

              // 按钮
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Get.back(),
                    child: const Text('取消'),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton(
                    onPressed: () async {
                      final apiKey = apiKeyController.text.trim();
                      if (apiKey.isNotEmpty) {
                        // 更新模型配置
                        await apiController.updateModelConfig(
                          model.name,
                          apiKey: apiKey,
                        );

                        // 切换到该模型
                        apiController.updateSelectedModel(model.name);

                        Get.back(); // 关闭快速配置弹窗
                        Get.back(); // 关闭模型选择弹窗

                        Get.snackbar(
                          '配置成功',
                          '${model.name} 已配置并切换',
                          backgroundColor: Colors.green.withOpacity(0.1),
                          duration: const Duration(seconds: 2),
                        );
                      } else {
                        Get.snackbar(
                          '错误',
                          'API密钥不能为空',
                          backgroundColor: Colors.red.withOpacity(0.1),
                        );
                      }
                    },
                    child: const Text('保存并使用'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建知识库选择器（弹窗式）
  Widget _buildKnowledgeBaseSelector() {
    final knowledgeController = Get.find<KnowledgeBaseController>();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Text(
              '知识库',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const Spacer(),
            Obx(() => Switch(
              value: knowledgeController.useKnowledgeBase.value,
              onChanged: (value) {
                knowledgeController.useKnowledgeBase.value = value;
                knowledgeController.saveSettings();
              },
            )),
          ],
        ),
        const SizedBox(height: 8),
        Obx(() => knowledgeController.useKnowledgeBase.value
            ? _buildKnowledgeSelectionButton()
            : Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Theme.of(context).disabledColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Text(
                  '知识库已禁用',
                  style: TextStyle(fontSize: 14),
                ),
              )),
      ],
    );
  }

  /// 构建知识库选择按钮
  Widget _buildKnowledgeSelectionButton() {
    final knowledgeController = Get.find<KnowledgeBaseController>();
    
    return InkWell(
      onTap: () => _showKnowledgeSelectionDialog(),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
        decoration: BoxDecoration(
          border: Border.all(color: Theme.of(context).dividerColor),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(
              Icons.library_books,
              size: 16,
              color: Theme.of(context).primaryColor,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                _selectedKnowledgeIds.isEmpty
                    ? '选择知识库文档'
                    : '已选择 ${_selectedKnowledgeIds.length} 个文档',
                style: TextStyle(
                  fontSize: 14,
                  color: _selectedKnowledgeIds.isEmpty
                      ? Colors.grey.shade600
                      : Theme.of(context).colorScheme.onSurface,
                ),
              ),
            ),
            Icon(
              Icons.arrow_drop_down,
              color: Theme.of(context).primaryColor,
            ),
          ],
        ),
      ),
    );
  }

  /// 显示知识库选择弹窗
  void _showKnowledgeSelectionDialog() {
    final knowledgeController = Get.find<KnowledgeBaseController>();
    final tempSelected = Set<String>.from(_selectedKnowledgeIds);

    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.8,
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.7,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 标题栏
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primaryContainer,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        '选择知识库文档',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onPrimaryContainer,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => Get.back(),
                      icon: Icon(
                        Icons.close,
                        color: Theme.of(context).colorScheme.onPrimaryContainer,
                      ),
                    ),
                  ],
                ),
              ),

              // 文档列表
              Flexible(
                child: Obx(() => ListView.builder(
                  shrinkWrap: true,
                  itemCount: knowledgeController.documents.length,
                  itemBuilder: (context, index) {
                    final doc = knowledgeController.documents[index];
                    final isSelected = tempSelected.contains(doc.id);

                    return CheckboxListTile(
                      title: Text(doc.title),
                      subtitle: Text(doc.category),
                      value: isSelected,
                      onChanged: (value) {
                        if (value == true) {
                          tempSelected.add(doc.id);
                        } else {
                          tempSelected.remove(doc.id);
                        }
                      },
                    );
                  },
                )),
              ),

              // 按钮栏
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: () => Get.back(),
                      child: const Text('取消'),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton(
                      onPressed: () {
                        setState(() {
                          _selectedKnowledgeIds.clear();
                          _selectedKnowledgeIds.addAll(tempSelected);
                        });
                        widget.onKnowledgeSelectionChanged(_selectedKnowledgeIds);
                        Get.back();
                      },
                      child: const Text('确定'),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建文风包选择器（弹窗式）
  Widget _buildWritingStyleSelector() {
    try {
      final styleController = Get.find<WritingStylePackageController>();

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '文风包',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          InkWell(
            onTap: () => _showWritingStyleSelectionDialog(),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
              decoration: BoxDecoration(
                border: Border.all(color: Theme.of(context).dividerColor),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.style,
                    size: 16,
                    color: Theme.of(context).primaryColor,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _getSelectedWritingStyleName(styleController),
                      style: TextStyle(
                        fontSize: 14,
                        color: _selectedWritingStyleId == null
                            ? Colors.grey.shade600
                            : Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                  ),
                  Icon(
                    Icons.arrow_drop_down,
                    color: Theme.of(context).primaryColor,
                  ),
                ],
              ),
            ),
          ),
        ],
      );
    } catch (e) {
      return const SizedBox.shrink();
    }
  }

  /// 获取选中的文风包名称
  String _getSelectedWritingStyleName(WritingStylePackageController controller) {
    if (_selectedWritingStyleId == null) {
      return '选择文风包';
    }
    final package = controller.packages.firstWhereOrNull(
      (p) => p.id == _selectedWritingStyleId,
    );
    return package?.name ?? '未知文风包';
  }

  /// 显示文风包选择弹窗
  void _showWritingStyleSelectionDialog() {
    final styleController = Get.find<WritingStylePackageController>();

    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.8,
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.6,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 标题栏
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primaryContainer,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        '选择文风包',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onPrimaryContainer,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => Get.back(),
                      icon: Icon(
                        Icons.close,
                        color: Theme.of(context).colorScheme.onPrimaryContainer,
                      ),
                    ),
                  ],
                ),
              ),

              // 文风包列表
              Flexible(
                child: Obx(() => ListView(
                  shrinkWrap: true,
                  children: [
                    // 无选择选项
                    ListTile(
                      leading: Icon(
                        Icons.clear,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                      title: const Text('无'),
                      trailing: _selectedWritingStyleId == null
                          ? Icon(Icons.check, color: Theme.of(context).primaryColor)
                          : null,
                      selected: _selectedWritingStyleId == null,
                      onTap: () {
                        setState(() {
                          _selectedWritingStyleId = null;
                        });
                        widget.onWritingStyleChanged(null);
                        Get.back();
                      },
                    ),
                    const Divider(),
                    // 文风包选项
                    ...styleController.packages.map((package) {
                      final isSelected = _selectedWritingStyleId == package.id;
                      return ListTile(
                        leading: Icon(
                          Icons.style,
                          color: Theme.of(context).primaryColor,
                        ),
                        title: Text(package.name),
                        subtitle: package.description.isNotEmpty
                            ? Text(package.description)
                            : null,
                        trailing: isSelected
                            ? Icon(Icons.check, color: Theme.of(context).primaryColor)
                            : null,
                        selected: isSelected,
                        onTap: () {
                          setState(() {
                            _selectedWritingStyleId = package.id;
                          });
                          widget.onWritingStyleChanged(package.id);
                          Get.back();
                        },
                      );
                    }),
                  ],
                )),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建角色选择器（弹窗式）
  Widget _buildCharacterSelector() {
    try {
      final characterService = Get.find<CharacterCardService>();

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '角色卡片',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Obx(() => InkWell(
            onTap: () => _showCharacterSelectionDialog(),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
              decoration: BoxDecoration(
                border: Border.all(color: Theme.of(context).dividerColor),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.person,
                    size: 16,
                    color: Theme.of(context).primaryColor,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      characterService.cards.isEmpty
                          ? '暂无角色卡片'
                          : _selectedCharacterIds.isEmpty
                              ? '选择角色卡片'
                              : '已选择 ${_selectedCharacterIds.length} 个角色',
                      style: TextStyle(
                        fontSize: 14,
                        color: characterService.cards.isEmpty || _selectedCharacterIds.isEmpty
                            ? Colors.grey.shade600
                            : Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                  ),
                  if (characterService.cards.isNotEmpty)
                    Icon(
                      Icons.arrow_drop_down,
                      color: Theme.of(context).primaryColor,
                    ),
                ],
              ),
            ),
          )),
        ],
      );
    } catch (e) {
      return const SizedBox.shrink();
    }
  }

  /// 显示角色选择弹窗
  void _showCharacterSelectionDialog() {
    final characterService = Get.find<CharacterCardService>();
    final tempSelected = Set<String>.from(_selectedCharacterIds);

    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.8,
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.7,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 标题栏
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primaryContainer,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        '选择角色卡片',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onPrimaryContainer,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => Get.back(),
                      icon: Icon(
                        Icons.close,
                        color: Theme.of(context).colorScheme.onPrimaryContainer,
                      ),
                    ),
                  ],
                ),
              ),

              // 角色列表
              Flexible(
                child: Obx(() => characterService.cards.isEmpty
                    ? const Padding(
                        padding: EdgeInsets.all(32),
                        child: Text(
                          '暂无角色卡片',
                          style: TextStyle(fontSize: 16),
                          textAlign: TextAlign.center,
                        ),
                      )
                    : ListView.builder(
                        shrinkWrap: true,
                        itemCount: characterService.cards.length,
                        itemBuilder: (context, index) {
                          final character = characterService.cards[index];
                          final isSelected = tempSelected.contains(character.id);

                          return CheckboxListTile(
                            title: Text(character.name),
                            subtitle: Text(
                              character.characterTypeId.isNotEmpty
                                  ? character.characterTypeId
                                  : '未分类',
                            ),
                            value: isSelected,
                            onChanged: (value) {
                              if (value == true) {
                                tempSelected.add(character.id);
                              } else {
                                tempSelected.remove(character.id);
                              }
                            },
                          );
                        },
                      )),
              ),

              // 按钮栏
              if (characterService.cards.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TextButton(
                        onPressed: () => Get.back(),
                        child: const Text('取消'),
                      ),
                      const SizedBox(width: 8),
                      ElevatedButton(
                        onPressed: () {
                          setState(() {
                            _selectedCharacterIds.clear();
                            _selectedCharacterIds.addAll(tempSelected);
                          });
                          widget.onCharacterSelectionChanged(_selectedCharacterIds);
                          Get.back();
                        },
                        child: const Text('确定'),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建上下文选择器（弹窗式）
  Widget _buildContextSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '上下文关联',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        InkWell(
          onTap: () => _showContextSelectionDialog(),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
            decoration: BoxDecoration(
              border: Border.all(color: Theme.of(context).dividerColor),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.link,
                  size: 16,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    widget.novel.chapters.isEmpty
                        ? '暂无其他章节'
                        : _selectedContextChapters.isEmpty
                            ? '选择关联章节'
                            : '已选择 ${_selectedContextChapters.length} 个章节',
                    style: TextStyle(
                      fontSize: 14,
                      color: widget.novel.chapters.isEmpty || _selectedContextChapters.isEmpty
                          ? Colors.grey.shade600
                          : Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                ),
                if (widget.novel.chapters.isNotEmpty)
                  Icon(
                    Icons.arrow_drop_down,
                    color: Theme.of(context).primaryColor,
                  ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// 显示上下文选择弹窗
  void _showContextSelectionDialog() {
    final tempSelected = Set<int>.from(_selectedContextChapters);
    final currentChapterNumber = widget.novel.chapters.isNotEmpty &&
                                widget.currentChapterIndex < widget.novel.chapters.length
        ? widget.novel.chapters[widget.currentChapterIndex].number
        : -1;

    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.8,
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.7,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 标题栏
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primaryContainer,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        '选择关联章节',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onPrimaryContainer,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => Get.back(),
                      icon: Icon(
                        Icons.close,
                        color: Theme.of(context).colorScheme.onPrimaryContainer,
                      ),
                    ),
                  ],
                ),
              ),

              // 章节列表
              Flexible(
                child: widget.novel.chapters.isEmpty
                    ? const Padding(
                        padding: EdgeInsets.all(32),
                        child: Text(
                          '暂无其他章节',
                          style: TextStyle(fontSize: 16),
                          textAlign: TextAlign.center,
                        ),
                      )
                    : ListView.builder(
                        shrinkWrap: true,
                        itemCount: widget.novel.chapters.length,
                        itemBuilder: (context, index) {
                          final chapter = widget.novel.chapters[index];

                          // 不显示当前章节
                          if (chapter.number == currentChapterNumber) {
                            return const SizedBox.shrink();
                          }

                          final isSelected = tempSelected.contains(chapter.number);

                          return CheckboxListTile(
                            title: Text(
                              chapter.number == 0 ? '大纲' : '第${chapter.number}章',
                            ),
                            subtitle: Text(chapter.title),
                            value: isSelected,
                            onChanged: (value) {
                              if (value == true) {
                                tempSelected.add(chapter.number);
                              } else {
                                tempSelected.remove(chapter.number);
                              }
                            },
                          );
                        },
                      ),
              ),

              // 按钮栏
              if (widget.novel.chapters.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TextButton(
                        onPressed: () => Get.back(),
                        child: const Text('取消'),
                      ),
                      const SizedBox(width: 8),
                      ElevatedButton(
                        onPressed: () {
                          setState(() {
                            _selectedContextChapters.clear();
                            _selectedContextChapters.addAll(tempSelected);
                          });
                          widget.onContextSelectionChanged(_selectedContextChapters);
                          Get.back();
                        },
                        child: const Text('确定'),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建角色创造开关
  Widget _buildCharacterCreationToggle() {
    return Row(
      children: [
        const Text(
          '允许AI创造新角色',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const Spacer(),
        Switch(
          value: _allowCreateNewCharacters,
          onChanged: (value) {
            setState(() {
              _allowCreateNewCharacters = value;
            });
            widget.onAllowCreateCharactersChanged(value);
          },
        ),
      ],
    );
  }

  /// 构建提示词输入
  Widget _buildPromptInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Text(
              '写作指令',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(width: 8),
            Text(
              '(可选)',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        TextField(
          controller: widget.promptController,
          maxLines: 4,
          decoration: InputDecoration(
            hintText: '(可选) 请输入写作指令，例如：\n- 重写这段对话，让它更生动\n- 添加更多的环境描写\n- 增加角色的内心独白',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建后续情节发展输入
  Widget _buildPlotDevelopmentInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Text(
              '后续情节发展',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(width: 8),
            Tooltip(
              message: '当大纲中没有该章节内容时，需要描述本章的主要情节发展',
              child: Icon(
                Icons.help_outline,
                size: 16,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        TextField(
          controller: widget.plotDevelopmentController,
          maxLines: 3,
          decoration: InputDecoration(
            hintText: '请描述本章的主要情节发展...\n例如：主角遇到新的挑战，与反派展开对话',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建生成按钮
  Widget _buildGenerateButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: widget.isGenerating ? null : widget.onGenerate,
        icon: widget.isGenerating
            ? const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            : const Icon(Icons.auto_awesome),
        label: Text(widget.isGenerating ? '生成中...' : '开始生成'),
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 12),
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:novel_app/controllers/novel_controller.dart';
import 'package:novel_app/models/novel.dart';
import 'package:novel_app/models/generation_mode.dart';
import 'package:novel_app/widgets/common/animated_card.dart';

class NovelSelectionScreen extends StatelessWidget {
  final NovelController _novelController = Get.find<NovelController>();

  NovelSelectionScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('选择要扩展的小说'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '选择一本小说进行扩展',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              '将基于已有内容扩展大纲并生成后续章节',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: _buildNovelList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNovelList() {
    return Obx(() {
      if (_novelController.novels.isEmpty) {
        return const Center(
          child: Text('没有找到小说，请先创建一本小说'),
        );
      }

      return ListView.builder(
        itemCount: _novelController.novels.length,
        itemBuilder: (context, index) {
          final novel = _novelController.novels[index];
          return AnimatedCard(
            onTap: () => _selectNovel(novel),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              novel.title,
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              novel.genre,
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            '${novel.chapters.length} 章',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                          ),
                          Text(
                            '${novel.wordCount} 字',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        },
      );
    });
  }

  void _selectNovel(Novel novel) async {
    try {
      // 显示对话框让用户输入想要扩展到的总章节数
      final currentChapters =
          novel.chapters.where((ch) => ch.number > 0).length;
      // 创建一个TextEditingController用于获取用户输入
      final inputController = TextEditingController(
        text: (currentChapters + 5).toString(),
      );

      final result = await Get.dialog<Map<String, dynamic>>(
        StatefulBuilder(
          builder: (context, setState) {
            final targetChapters = int.tryParse(inputController.text) ?? (currentChapters + 5);

            return AlertDialog(
              title: Text('扩展《${novel.title}》'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('当前已有 $currentChapters 章'),
                  const SizedBox(height: 16),
                  const Text('请输入想要扩展到的总章节数：'),
                  TextField(
                    controller: inputController,
                    keyboardType: TextInputType.number,
                    decoration: const InputDecoration(
                      hintText: '请输入章节数',
                    ),
                    onChanged: (value) {
                      setState(() {});
                    },
                  ),
                  const SizedBox(height: 16),
                  // 添加章节数量提示
                  if (targetChapters > 300)
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.red.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(color: Colors.red.withOpacity(0.3)),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.warning, color: Colors.red, size: 16),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              '⚠️ 超过300章可能导致生成失败，程序员连夜优化算法中！',
                              style: TextStyle(
                                color: Colors.red,
                                fontSize: 12,
                              ),
                            ),
                          ),
                        ],
                      ),
                    )
                  else if (targetChapters > 90)
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.orange.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(color: Colors.orange.withOpacity(0.3)),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.info, color: Colors.orange, size: 16),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              '💡 建议不超过90章以获得更好的生成效果，程序员连夜优化算法中！',
                              style: TextStyle(
                                color: Colors.orange,
                                fontSize: 12,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  const SizedBox(height: 16),
                  const Text('注意：将基于已有内容生成后续章节的大纲，然后生成细纲和内容。'),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Get.back(),
                  child: const Text('取消'),
                ),
                ElevatedButton(
                  onPressed: () {
                    final totalChapters =
                        int.tryParse(inputController.text) ?? (currentChapters + 5);
                    Get.back(result: {
                      'totalChapters': totalChapters,
                    });
                  },
                  child: const Text('确定'),
                ),
              ],
            );
          },
        ),
      );

      if (result == null) {
        // 用户取消了操作
        return;
      }

      final totalChapters = result['totalChapters'] as int;

      // 设置为精简模式
      _novelController.generationMode.value = GenerationMode.lightweight;

      // 填充小说信息到主页面
      _novelController.updateTitle(novel.title);

      // 解析小说类型
      final genres = novel.genre.split(',');
      _novelController.selectedGenres.clear();
      _novelController.selectedGenres.addAll(genres);

      // 设置风格
      if (novel.style != null && novel.style!.isNotEmpty) {
        _novelController.updateStyle(novel.style!);
      }

      // 设置总章节数
      _novelController.updateTotalChapters(totalChapters);

      // 尝试恢复大纲并准备扩展
      await _novelController.prepareNovelForContinuation(novel, totalChapters);

      // 返回主页面
      Get.back();

      // 显示提示
      Get.snackbar(
        '小说已选择',
        '已选择《${novel.title}》进行扩展，将扩展到 $totalChapters 章',
        duration: const Duration(seconds: 3),
      );

      // 提示用户点击扩展按钮
      Future.delayed(const Duration(seconds: 1), () {
        Get.snackbar(
          '扩展提示',
          '请点击"生成大纲"按钮开始扩展小说大纲',
          duration: const Duration(seconds: 5),
        );
      });
    } catch (e) {
      Get.snackbar('错误', '选择小说失败: $e');
    }
  }
}

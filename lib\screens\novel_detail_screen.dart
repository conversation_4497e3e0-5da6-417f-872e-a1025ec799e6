import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:get/get.dart';
import 'package:novel_app/models/novel.dart';
import 'package:novel_app/services/content_review_service.dart';
import 'package:novel_app/controllers/api_config_controller.dart';
import 'package:novel_app/services/ai_service.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:novel_app/controllers/novel_controller.dart';
import 'package:novel_app/controllers/knowledge_base_controller.dart';
import 'package:novel_app/services/character_card_service.dart';
import 'package:novel_app/controllers/writing_style_package_controller.dart';
import 'package:novel_app/models/character_card.dart';
import 'package:novel_app/models/writing_style_package.dart';
import 'package:novel_app/widgets/centaur_writing_panel.dart';

class NovelDetailScreen extends StatefulWidget {
  final Novel novel;

  const NovelDetailScreen({super.key, required this.novel});

  @override
  State<NovelDetailScreen> createState() => _NovelDetailScreenState();
}

class _NovelDetailScreenState extends State<NovelDetailScreen> {
  final NovelController _controller = Get.find<NovelController>();
  final TextEditingController _titleController = TextEditingController();
  final List<TextEditingController> _chapterControllers = [];
  final List<String> _undoHistory = [];
  final List<String> _redoHistory = [];
  bool _isEditing = false;
  bool _isMarkdownMode = false; // 新增：控制markdown渲染模式
  int _currentChapterIndex = 0;
  late Novel _currentNovel;
  TextSelection? _lastSelection;
  final _aiService = Get.find<AIService>();

  // 半人马写作侧边栏相关状态
  bool _showCentaurSidebar = false;
  final _centaurPromptController = TextEditingController();
  final _plotDevelopmentController = TextEditingController();
  bool _isGeneratingWithCentaur = false;
  bool _allowCreateNewCharacters = true;
  final _selectedKnowledgeIds = <String>{};
  final _selectedCharacterIds = <String>{};
  final _selectedContextChapters = <int>{};
  String? _selectedWritingStyleId;

  // 大纲查看器相关状态
  int _selectedOutlineSectionIndex = 0;
  final ScrollController _outlineScrollController = ScrollController();
  TextEditingController? _outlineEditController;

  // 章节编辑器滚动控制器
  final ScrollController _chapterTextScrollController = ScrollController();
  List<OutlineChapterData> _outlineChapters = [];

  @override
  void initState() {
    super.initState();
    _currentNovel = widget.novel.copyWith();
    _titleController.text = _currentNovel.title;
    _initChapterControllers();
  }

  void _initChapterControllers() {
    _chapterControllers.clear();
    for (var chapter in _currentNovel.chapters) {
      final controller = TextEditingController(text: chapter.content);
      controller.addListener(() {
        // 当文本发生变化时保存到历史记录
        if (_isEditing) {
          _saveCurrentToHistory();
        }
      });
      _chapterControllers.add(controller);
    }
  }

  void _saveCurrentToHistory() {
    if (_currentChapterIndex < _currentNovel.chapters.length) {
      // 只有当内容真正发生变化时才保存历史
      final currentChapter = _currentNovel.chapters[_currentChapterIndex];
      if (_undoHistory.isEmpty || _undoHistory.last != currentChapter.content) {
        _undoHistory.add(currentChapter.content);
        // 添加新的历史记录时清空重做历史
        _redoHistory.clear();
        setState(() {}); // 更新UI状态，使撤销按钮可用
      }
    }
  }

  void _undo() {
    if (_undoHistory.isNotEmpty) {
      // 保存当前状态到重做历史
      _redoHistory.add(_chapterControllers[_currentChapterIndex].text);
      // 恢复上一个状态
      final lastState = _undoHistory.removeLast();
      _chapterControllers[_currentChapterIndex].text = lastState;

      // 更新小说对象
      final updatedChapters = List<Chapter>.from(_currentNovel.chapters);
      updatedChapters[_currentChapterIndex] =
          updatedChapters[_currentChapterIndex].copyWith(
        content: lastState,
      );
      _updateNovel(_currentNovel.copyWith(chapters: updatedChapters));
    }
  }

  void _redo() {
    if (_redoHistory.isNotEmpty) {
      // 保存当前状态到撤销历史
      _undoHistory.add(_chapterControllers[_currentChapterIndex].text);
      // 恢复下一个状态
      final nextState = _redoHistory.removeLast();
      _chapterControllers[_currentChapterIndex].text = nextState;

      // 更新小说对象
      final updatedChapters = List<Chapter>.from(_currentNovel.chapters);
      updatedChapters[_currentChapterIndex] =
          updatedChapters[_currentChapterIndex].copyWith(
        content: nextState,
      );
      _updateNovel(_currentNovel.copyWith(chapters: updatedChapters));
    }
  }

  void _saveChanges() async {
    try {
      Novel finalNovel;

      if (_currentNovel.chapters.isEmpty) {
        // 短篇小说：直接保存内容
        finalNovel = _currentNovel.copyWith(
          title: _titleController.text,
        );
      } else {
        // 长篇小说：保存章节内容
        // 创建更新后的小说对象
        final updatedNovel = _currentNovel.copyWith(
          title: _titleController.text,
        );

        // 更新所有章节内容
        final updatedChapters = List<Chapter>.from(_currentNovel.chapters);
        for (var i = 0; i < _currentNovel.chapters.length; i++) {
          final chapter = _currentNovel.chapters[i];
          String newContent;

          // 如果是大纲章节且有结构化大纲数据，生成格式化内容
          if (chapter.title == '大纲' && _outlineChapters.isNotEmpty) {
            newContent = _generateOutlineContent();
          } else {
            // 否则使用对应的章节控制器内容
            newContent = _chapterControllers[i].text;
          }

          updatedChapters[i] = updatedChapters[i].copyWith(
            content: newContent,
          );
        }

        // 更新小说内容
        finalNovel = updatedNovel.copyWith(
          chapters: updatedChapters,
          content: updatedChapters.map((c) => c.content).join('\n\n'),
        );
      }

      // 保存到数据库
      await _controller.saveNovel(finalNovel);

      // 更新本地状态
      _updateNovel(finalNovel);

      setState(() {
        _isEditing = false;
        // 清空历史记录
        _undoHistory.clear();
        _redoHistory.clear();
        // 重置大纲编辑控制器
        _outlineEditController?.dispose();
        _outlineEditController = null;
        // 清理大纲章节数据
        for (var chapter in _outlineChapters) {
          chapter.dispose();
        }
        _outlineChapters.clear();
      });

      Get.snackbar(
        '保存成功',
        '所有修改已保存',
        snackPosition: SnackPosition.BOTTOM,
        duration: const Duration(seconds: 2),
      );
    } catch (e) {
      Get.snackbar(
        '保存失败',
        '发生错误：$e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red[100],
        duration: const Duration(seconds: 3),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: _isEditing
            ? TextField(
                controller: _titleController,
                style: const TextStyle(color: Colors.white),
                decoration: const InputDecoration(
                  border: InputBorder.none,
                  hintText: '输入小说标题',
                  hintStyle: TextStyle(color: Colors.white70),
                ),
              )
            : Text(_currentNovel.title),
        actions: [
          if (_isEditing) ...[
            IconButton(
              icon: const Icon(Icons.undo),
              onPressed: _undoHistory.isEmpty ? null : _undo,
              tooltip: '撤销',
            ),
            IconButton(
              icon: const Icon(Icons.redo),
              onPressed: _redoHistory.isEmpty ? null : _redo,
              tooltip: '重做',
            ),
            IconButton(
              icon: Icon(_showCentaurSidebar ? Icons.auto_awesome : Icons.auto_awesome_outlined),
              onPressed: () {
                setState(() {
                  _showCentaurSidebar = !_showCentaurSidebar;
                });
              },
              tooltip: '半人马写作',
            ),
            IconButton(
              icon: const Icon(Icons.save),
              onPressed: _saveChanges,
              tooltip: '保存',
            ),
          ] else ...[
            // 非编辑模式下显示markdown渲染切换按钮
            IconButton(
              icon: Icon(_isMarkdownMode ? Icons.text_fields : Icons.code),
              onPressed: () {
                setState(() {
                  _isMarkdownMode = !_isMarkdownMode;
                });
              },
              tooltip: _isMarkdownMode ? '普通文本' : 'Markdown渲染',
            ),
          ],
          IconButton(
            icon: Icon(_isEditing ? Icons.close : Icons.edit),
            onPressed: () {
              if (_isEditing) {
                // 取消编辑，恢复原始内容
                _titleController.text = _currentNovel.title;
                _initChapterControllers();
                _undoHistory.clear();
                _redoHistory.clear();
                // 重置大纲编辑控制器
                _outlineEditController?.dispose();
                _outlineEditController = null;
                // 清理大纲章节数据
                for (var chapter in _outlineChapters) {
                  chapter.dispose();
                }
                _outlineChapters.clear();
              }
              setState(() {
                _isEditing = !_isEditing;
              });
            },
            tooltip: _isEditing ? '取消' : '编辑',
          ),
        ],
      ),
      body: _isEditing && _showCentaurSidebar
          ? Row(
              children: [
                // 主编辑区域
                Expanded(
                  flex: 2,
                  child: Column(
                    children: [
                      _buildChapterList(),
                      Expanded(
                        child: _buildChapterContent(),
                      ),
                    ],
                  ),
                ),
                // 半人马写作侧边栏
                Container(
                  width: 400,
                  decoration: BoxDecoration(
                    border: Border(
                      left: BorderSide(
                        color: Theme.of(context).dividerColor,
                        width: 1,
                      ),
                    ),
                  ),
                  child: CentaurWritingPanel(
                    novel: _currentNovel,
                    currentChapterIndex: _currentChapterIndex,
                    promptController: _centaurPromptController,
                    plotDevelopmentController: _plotDevelopmentController,
                    isGenerating: _isGeneratingWithCentaur,
                    onGenerate: _generateWithCentaur,
                    // 传递当前状态值
                    initialSelectedKnowledgeIds: _selectedKnowledgeIds,
                    initialSelectedCharacterIds: _selectedCharacterIds,
                    initialSelectedContextChapters: _selectedContextChapters,
                    initialSelectedWritingStyleId: _selectedWritingStyleId,
                    initialAllowCreateNewCharacters: _allowCreateNewCharacters,
                    onKnowledgeSelectionChanged: (selected) {
                      setState(() {
                        _selectedKnowledgeIds.clear();
                        _selectedKnowledgeIds.addAll(selected);
                      });
                    },
                    onCharacterSelectionChanged: (selected) {
                      setState(() {
                        _selectedCharacterIds.clear();
                        _selectedCharacterIds.addAll(selected);
                      });
                    },
                    onContextSelectionChanged: (selected) {
                      setState(() {
                        _selectedContextChapters.clear();
                        _selectedContextChapters.addAll(selected);
                      });
                    },
                    onWritingStyleChanged: (styleId) {
                      setState(() {
                        _selectedWritingStyleId = styleId;
                      });
                    },
                    onAllowCreateCharactersChanged: (allow) {
                      setState(() {
                        _allowCreateNewCharacters = allow;
                      });
                    },
                  ),
                ),
              ],
            )
          : Column(
              children: [
                _buildChapterList(),
                Expanded(
                  child: _buildChapterContent(),
                ),
              ],
            ),
    );
  }

  Widget _buildChapterList() {
    // 如果是短篇小说（没有章节），显示创建章节的提示
    if (_currentNovel.chapters.isEmpty) {
      return Container(
        height: 50,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Row(
          children: [
            const Text('暂无章节'),
            const SizedBox(width: 16),
            ElevatedButton.icon(
              onPressed: () => _createNewChapter(),
              icon: const Icon(Icons.add, size: 16),
              label: const Text('创建第一章'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                minimumSize: const Size(0, 32),
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      height: 50,
      child: Row(
        children: [
          Expanded(
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _currentNovel.chapters.length,
              itemBuilder: (context, index) {
                return Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 4),
                  child: ChoiceChip(
                    label: Text(_currentNovel.chapters[index].title == '大纲'
                        ? '大纲'
                        : '第${_currentNovel.chapters[index].number}章'),
                    selected: _currentChapterIndex == index,
                    onSelected: (selected) {
                      if (selected) {
                        setState(() {
                          _currentChapterIndex = index;
                        });
                      }
                    },
                  ),
                );
              },
            ),
          ),
          // 添加新章节按钮
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: IconButton(
              onPressed: () => _createNewChapter(),
              icon: const Icon(Icons.add),
              tooltip: '添加新章节',
              style: IconButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primaryContainer,
                foregroundColor: Theme.of(context).colorScheme.onPrimaryContainer,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChapterContent() {
    // 如果是短篇小说（没有章节），直接显示小说内容
    if (_currentNovel.chapters.isEmpty) {
      return _buildShortNovelContent();
    }

    if (_currentChapterIndex >= _currentNovel.chapters.length) {
      return const Center(child: Text('暂无内容'));
    }

    final chapter = _currentNovel.chapters[_currentChapterIndex];

    // 如果是大纲章节，使用专门的大纲查看器
    if (chapter.title == '大纲') {
      return _buildOutlineViewer(chapter);
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            chapter.title,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _isEditing
              ? TextField(
                  controller: _chapterControllers[_currentChapterIndex],
                  scrollController: _chapterTextScrollController,
                  maxLines: null,
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                    hintText: '输入章节内容',
                  ),
                  contextMenuBuilder: (context, editableTextState) {
                    final List<ContextMenuButtonItem> buttonItems =
                        editableTextState.contextMenuButtonItems;

                    if (editableTextState.textEditingValue.selection.isValid &&
                        !editableTextState
                            .textEditingValue.selection.isCollapsed) {
                      _lastSelection =
                          editableTextState.textEditingValue.selection;
                      final selectedText = _lastSelection!.textInside(
                          _chapterControllers[_currentChapterIndex].text);

                      return AdaptiveTextSelectionToolbar(
                        anchors: editableTextState.contextMenuAnchors,
                        children: [
                          TextSelectionToolbarTextButton(
                            padding: const EdgeInsets.all(12.0),
                            onPressed: () {
                              editableTextState.hideToolbar();
                              _showAIEditDialog(selectedText);
                            },
                            child: const Text(
                              'AI编辑',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.blue,
                              ),
                            ),
                          ),
                          const VerticalDivider(
                            width: 1,
                            indent: 8,
                            endIndent: 8,
                          ),
                          TextSelectionToolbarTextButton(
                            padding: const EdgeInsets.all(12.0),
                            onPressed: () {
                              Clipboard.setData(
                                  ClipboardData(text: selectedText));
                              editableTextState.hideToolbar();
                            },
                            child: const Text(
                              '复制',
                              style: TextStyle(fontSize: 14),
                            ),
                          ),
                          const VerticalDivider(
                            width: 1,
                            indent: 8,
                            endIndent: 8,
                          ),
                          TextSelectionToolbarTextButton(
                            padding: const EdgeInsets.all(12.0),
                            onPressed: () {
                              Clipboard.setData(
                                  ClipboardData(text: selectedText));
                              // 更新文本内容，删除选中部分
                              final text =
                                  _chapterControllers[_currentChapterIndex]
                                      .text;
                              final newText = text.replaceRange(
                                  _lastSelection!.start,
                                  _lastSelection!.end,
                                  '');
                              _chapterControllers[_currentChapterIndex].value =
                                  TextEditingValue(
                                text: newText,
                                selection: TextSelection.collapsed(
                                    offset: _lastSelection!.start),
                              );
                              editableTextState.hideToolbar();
                            },
                            child: const Text(
                              '剪切',
                              style: TextStyle(fontSize: 14),
                            ),
                          ),
                          const VerticalDivider(
                            width: 1,
                            indent: 8,
                            endIndent: 8,
                          ),
                          TextSelectionToolbarTextButton(
                            padding: const EdgeInsets.all(12.0),
                            onPressed: () async {
                              final data =
                                  await Clipboard.getData('text/plain');
                              if (data?.text != null) {
                                // 更新文本内容，插入剪贴板内容
                                final text =
                                    _chapterControllers[_currentChapterIndex]
                                        .text;
                                final newText = text.replaceRange(
                                    _lastSelection!.start,
                                    _lastSelection!.end,
                                    data!.text!);
                                _chapterControllers[_currentChapterIndex]
                                    .value = TextEditingValue(
                                  text: newText,
                                  selection: TextSelection.collapsed(
                                      offset: _lastSelection!.start +
                                          data.text!.length),
                                );
                              }
                              editableTextState.hideToolbar();
                            },
                            child: const Text(
                              '粘贴',
                              style: TextStyle(fontSize: 14),
                            ),
                          ),
                        ],
                      );
                    }

                    return AdaptiveTextSelectionToolbar.buttonItems(
                      anchors: editableTextState.contextMenuAnchors,
                      buttonItems: buttonItems,
                    );
                  },
                )
              : _isMarkdownMode
                  ? MarkdownBody(
                      data: chapter.content,
                      selectable: true,
                      styleSheet: MarkdownStyleSheet(
                        p: TextStyle(
                          fontSize: 16,
                          height: 1.8,
                          color: Theme.of(context).colorScheme.onSurface,
                          fontWeight: FontWeight.w400,
                        ),
                        h1: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                        h2: TextStyle(
                          fontSize: 22,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                        h3: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                        blockquote: TextStyle(
                          fontSize: 16,
                          fontStyle: FontStyle.italic,
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.8),
                        ),
                        code: TextStyle(
                          fontSize: 14,
                          fontFamily: 'monospace',
                          backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    )
                  : SelectableText(
                      chapter.content,
                      style: TextStyle(
                        fontSize: 16,
                        height: 1.8,
                        // 使用主题的onSurface颜色，确保在深色模式下有足够的对比度
                        color: Theme.of(context).colorScheme.onSurface,
                        // 增加字体粗细，提高可读性
                        fontWeight: FontWeight.w400,
                      ),
                      onSelectionChanged: (selection, cause) {
                        if (!selection.isCollapsed) {
                          _lastSelection = selection;
                        }
                      },
                      contextMenuBuilder: (context, editableTextState) {
                        if (editableTextState.textEditingValue.selection.isValid &&
                            !editableTextState
                                .textEditingValue.selection.isCollapsed) {
                          final selectedText = editableTextState
                              .textEditingValue.selection
                              .textInside(chapter.content);

                          return AdaptiveTextSelectionToolbar(
                            anchors: editableTextState.contextMenuAnchors,
                            children: [
                              TextSelectionToolbarTextButton(
                                padding: const EdgeInsets.all(12.0),
                                onPressed: () {
                                  editableTextState.hideToolbar();
                                  _showAIEditDialog(selectedText);
                                },
                                child: const Text(
                                  'AI编辑',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.blue,
                                  ),
                                ),
                              ),
                              const VerticalDivider(
                                width: 1,
                                indent: 8,
                                endIndent: 8,
                              ),
                              TextSelectionToolbarTextButton(
                                padding: const EdgeInsets.all(12.0),
                                onPressed: () {
                                  Clipboard.setData(
                                      ClipboardData(text: selectedText));
                                  editableTextState.hideToolbar();
                                },
                                child: const Text(
                                  '复制',
                                  style: TextStyle(fontSize: 14),
                                ),
                              ),
                            ],
                          );
                        }

                        return AdaptiveTextSelectionToolbar.buttonItems(
                          anchors: editableTextState.contextMenuAnchors,
                          buttonItems: editableTextState.contextMenuButtonItems,
                        );
                      },
                    ),
        ],
      ),
    );
  }

  // 构建短篇小说内容显示
  Widget _buildShortNovelContent() {
    final TextEditingController contentController =
        TextEditingController(text: _currentNovel.content);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            _currentNovel.title,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _isEditing
              ? TextField(
                  controller: contentController,
                  maxLines: null,
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                    hintText: '输入小说内容',
                  ),
                  onChanged: (value) {
                    // 实时更新小说内容
                    _currentNovel = _currentNovel.copyWith(content: value);
                  },
                )
              : _isMarkdownMode
                  ? MarkdownBody(
                      data: _currentNovel.content.isEmpty
                          ? '暂无内容'
                          : _currentNovel.content,
                      selectable: true,
                      styleSheet: MarkdownStyleSheet(
                        p: TextStyle(
                          fontSize: 16,
                          height: 1.8,
                          color: Theme.of(context).colorScheme.onSurface,
                          fontWeight: FontWeight.w400,
                        ),
                        h1: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                        h2: TextStyle(
                          fontSize: 22,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                        h3: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                        blockquote: TextStyle(
                          fontSize: 16,
                          fontStyle: FontStyle.italic,
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.8),
                        ),
                        code: TextStyle(
                          fontSize: 14,
                          fontFamily: 'monospace',
                          backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    )
                  : SelectableText(
                      _currentNovel.content.isEmpty
                          ? '暂无内容'
                          : _currentNovel.content,
                      style: TextStyle(
                        fontSize: 16,
                        height: 1.8,
                        color: Theme.of(context).colorScheme.onSurface,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _titleController.dispose();
    for (var controller in _chapterControllers) {
      controller.dispose();
    }
    _outlineScrollController.dispose();
    _chapterTextScrollController.dispose();
    _outlineEditController?.dispose();
    for (var chapter in _outlineChapters) {
      chapter.dispose();
    }
    _centaurPromptController.dispose();
    _plotDevelopmentController.dispose();
    super.dispose();
  }

  void _updateNovel(Novel newNovel) {
    setState(() {
      _currentNovel = newNovel;
    });
  }

  Future<void> _showAIEditDialog(String selectedText) async {
    final promptController = TextEditingController();
    final result = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('AI编辑文本'),
        content: SizedBox(
          width: double.maxFinite,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('已选中文本：',
                    style: TextStyle(fontWeight: FontWeight.bold)),
                Container(
                  constraints: BoxConstraints(
                    maxHeight: MediaQuery.of(context).size.height * 0.3,
                  ),
                  decoration: BoxDecoration(
                    color: Theme.of(context).scaffoldBackgroundColor,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  margin: const EdgeInsets.symmetric(vertical: 8),
                  padding: const EdgeInsets.all(8),
                  child: SingleChildScrollView(
                    child: Text(
                      selectedText,
                      style: TextStyle(
                        // 使用主题的onSurface颜色，确保在深色模式下有足够的对比度
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: promptController,
                  decoration: const InputDecoration(
                    labelText: '修改指令',
                    hintText: '例如：改写成更生动的描述',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(promptController.text),
            child: const Text('确定'),
          ),
        ],
      ),
    );

    if (result != null && result.isNotEmpty) {
      _modifyTextWithAI(selectedText, result);
    }
  }

  Future<void> _modifyTextWithAI(String originalText, String prompt) async {
    final modifiedTextController = TextEditingController();

    try {
      Get.dialog(
        AlertDialog(
          title: const Text('AI正在修改文本'),
          content: SizedBox(
            width: double.maxFinite,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const LinearProgressIndicator(),
                  const SizedBox(height: 16),
                  TextField(
                    controller: modifiedTextController,
                    maxLines: null,
                    readOnly: true,
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      contentPadding: EdgeInsets.all(8),
                    ),
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text('取消'),
            ),
          ],
        ),
        barrierDismissible: false,
      );

      final systemPrompt = '''请根据用户的要求修改以下文本。要求：
1. 保持修改后的文本与上下文的连贯性
2. 保持人物、场景等设定的一致性
3. 确保修改后的文本符合整体风格
4. 避免出现与原意相违背的内容

原文：
$originalText

用户要求：
$prompt''';

      const userPrompt = '请按照上述要求修改文本，直接输出修改后的内容，不需要其他解释。';

      String modifiedText = '';
      await for (final chunk in _aiService.generateTextStream(
        systemPrompt: systemPrompt,
        userPrompt: userPrompt,
        maxTokens: 2000,
        temperature: 0.7,
      )) {
        modifiedText += chunk;
        modifiedTextController.text = modifiedText;
      }

      Get.back(); // 关闭生成对话框

      if (_lastSelection != null) {
        if (_isEditing) {
          // 编辑模式下直接更新文本控制器
          final text = _chapterControllers[_currentChapterIndex].text;
          final start = _lastSelection!.start;
          final end = _lastSelection!.end;

          if (start >= 0 && end <= text.length && start <= end) {
            final newText = text.replaceRange(start, end, modifiedText.trim());
            _chapterControllers[_currentChapterIndex].value = TextEditingValue(
              text: newText,
              selection: TextSelection.collapsed(
                  offset: start + modifiedText.trim().length),
            );

            // 标记有更改，但不立即保存
            setState(() {
              // 保存当前状态到历史记录
              _saveCurrentToHistory();
            });
          }
        } else {
          // 非编辑模式下更新章节内容
          final chapter = _currentNovel.chapters[_currentChapterIndex];
          final originalContent = chapter.content;
          final start = _lastSelection!.start;
          final end = _lastSelection!.end;

          if (start >= 0 && end <= originalContent.length && start <= end) {
            final newContent =
                originalContent.replaceRange(start, end, modifiedText.trim());

            // 创建更新后的章节
            final updatedChapter = chapter.copyWith(content: newContent);

            // 更新小说对象
            final updatedChapters = List<Chapter>.from(_currentNovel.chapters);
            updatedChapters[_currentChapterIndex] = updatedChapter;

            // 更新小说
            final updatedNovel = _currentNovel.copyWith(
              chapters: updatedChapters,
              content: updatedChapters.map((c) => c.content).join('\n\n'),
            );

            // 保存到数据库
            _controller.saveNovel(updatedNovel);

            // 更新本地状态
            _updateNovel(updatedNovel);

            // 更新编辑器控制器，以防后续切换到编辑模式
            if (_currentChapterIndex < _chapterControllers.length) {
              _chapterControllers[_currentChapterIndex].text = newContent;
            }
          }
        }

        Get.snackbar(
          '修改完成',
          '文本已更新',
          backgroundColor: Colors.green.withAlpha(25),
          duration: const Duration(seconds: 2),
        );
      }
    } catch (e) {
      // 确保对话框已关闭
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      Get.snackbar(
        '修改失败',
        e.toString(),
        backgroundColor: Colors.red.withAlpha(25),
        duration: const Duration(seconds: 3),
      );
    } finally {
      // 确保资源被释放
      modifiedTextController.dispose();
    }
  }

  /// 构建大纲查看器，支持分块显示和索引
  Widget _buildOutlineViewer(Chapter outlineChapter) {
    // 如果是编辑模式，显示结构化编辑器
    if (_isEditing) {
      _initOutlineChapters(outlineChapter.content);
      return _buildStructuredOutlineEditor();
    }

    // 查看模式：显示格式化的大纲
    final outlineContent = outlineChapter.content;
    final outlineSections = _parseOutlineSections(outlineContent);

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 左侧索引栏
        Container(
          width: 200,
          height: double.infinity,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceContainerHighest,
            border: Border(
              right: BorderSide(
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
          ),
          child: _buildOutlineIndex(outlineSections),
        ),
        // 右侧内容区域
        Expanded(
          child: _buildOutlineContent(outlineSections),
        ),
      ],
    );
  }

  /// 解析大纲内容为分块结构
  List<OutlineSection> _parseOutlineSections(String content) {
    final sections = <OutlineSection>[];
    final lines = content.split('\n');

    OutlineSection? currentSection;
    final contentBuffer = StringBuffer();

    for (int i = 0; i < lines.length; i++) {
      final line = lines[i];
      final trimmedLine = line.trim();

      if (trimmedLine.isEmpty) {
        contentBuffer.writeln(line);
        continue;
      }

      // 检测章节标题（第X章：标题 或 第X章 标题）
      final chapterMatch = RegExp(r'^第(\d+)章[：:\s]*(.*)$').firstMatch(trimmedLine);
      if (chapterMatch != null) {
        // 保存前一个章节
        if (currentSection != null) {
          currentSection.content = contentBuffer.toString().trim();
          if (currentSection.content.isNotEmpty) {
            sections.add(currentSection);
          }
          contentBuffer.clear();
        }

        // 创建新章节
        final chapterNumber = int.tryParse(chapterMatch.group(1)!) ?? 0;
        final chapterTitle = chapterMatch.group(2)?.trim() ?? '';
        final displayTitle = chapterTitle.isNotEmpty
          ? '第$chapterNumber章：$chapterTitle'
          : '第$chapterNumber章';

        currentSection = OutlineSection(
          id: 'chapter_$chapterNumber',
          title: displayTitle,
          type: OutlineSectionType.chapter,
          content: '',
        );
        continue;
      }

      // 检测主要标题（# 标题）
      final h1Match = RegExp(r'^#+\s+(.+)$').firstMatch(trimmedLine);
      if (h1Match != null) {
        // 保存前一个部分
        if (currentSection != null) {
          currentSection.content = contentBuffer.toString().trim();
          if (currentSection.content.isNotEmpty) {
            sections.add(currentSection);
          }
          contentBuffer.clear();
        }

        // 创建新部分
        final title = h1Match.group(1)!.trim();
        final level = trimmedLine.indexOf(' ');
        final type = level <= 2 ? OutlineSectionType.section : OutlineSectionType.subsection;

        currentSection = OutlineSection(
          id: 'section_${sections.length}',
          title: title,
          type: type,
          content: '',
        );
        continue;
      }

      // 检测特殊格式的标题（如：小说标题：、生成时间：等）
      final specialMatch = RegExp(r'^([^：:]+)[：:](.+)$').firstMatch(trimmedLine);
      if (specialMatch != null) {
        final key = specialMatch.group(1)!.trim();
        final value = specialMatch.group(2)!.trim();

        // 如果是重要的元信息，创建单独的部分
        if (['小说标题', '生成时间', '总章节数', '世界观设定', '详细大纲'].contains(key)) {
          // 保存前一个部分
          if (currentSection != null) {
            currentSection.content = contentBuffer.toString().trim();
            if (currentSection.content.isNotEmpty) {
              sections.add(currentSection);
            }
            contentBuffer.clear();
          }

          currentSection = OutlineSection(
            id: 'meta_${sections.length}',
            title: key,
            type: OutlineSectionType.subsection,
            content: value,
          );
          continue;
        }
      }

      // 添加内容行
      contentBuffer.writeln(line);
    }

    // 保存最后一个部分
    if (currentSection != null) {
      currentSection.content = contentBuffer.toString().trim();
      if (currentSection.content.isNotEmpty) {
        sections.add(currentSection);
      }
    }

    // 如果没有找到任何结构化内容，创建一个默认部分
    if (sections.isEmpty) {
      sections.add(OutlineSection(
        id: 'default',
        title: '大纲内容',
        type: OutlineSectionType.section,
        content: content,
      ));
    }

    return sections;
  }

  /// 构建左侧索引栏
  Widget _buildOutlineIndex(List<OutlineSection> sections) {
    return ListView.builder(
      padding: const EdgeInsets.all(8),
      itemCount: sections.length,
      itemBuilder: (context, index) {
        final section = sections[index];
        return _buildIndexItem(section, index);
      },
    );
  }

  /// 构建索引项
  Widget _buildIndexItem(OutlineSection section, int index) {
    final isSelected = _selectedOutlineSectionIndex == index;

    return Container(
      margin: const EdgeInsets.only(bottom: 4),
      child: Material(
        color: isSelected
          ? Theme.of(context).colorScheme.primaryContainer
          : Colors.transparent,
        borderRadius: BorderRadius.circular(8),
        child: InkWell(
          borderRadius: BorderRadius.circular(8),
          onTap: () {
            setState(() {
              _selectedOutlineSectionIndex = index;
            });
            // 滚动到对应内容
            _scrollToSection(index);
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            child: Row(
              children: [
                // 类型图标
                Icon(
                  _getSectionIcon(section.type),
                  size: 16,
                  color: isSelected
                    ? Theme.of(context).colorScheme.onPrimaryContainer
                    : Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                const SizedBox(width: 8),
                // 标题
                Expanded(
                  child: Text(
                    section.title,
                    style: TextStyle(
                      fontSize: _getSectionFontSize(section.type),
                      fontWeight: _getSectionFontWeight(section.type),
                      color: isSelected
                        ? Theme.of(context).colorScheme.onPrimaryContainer
                        : Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 获取章节类型对应的图标
  IconData _getSectionIcon(OutlineSectionType type) {
    switch (type) {
      case OutlineSectionType.chapter:
        return Icons.book;
      case OutlineSectionType.section:
        return Icons.folder;
      case OutlineSectionType.subsection:
        return Icons.description;
    }
  }

  /// 获取章节类型对应的字体大小
  double _getSectionFontSize(OutlineSectionType type) {
    switch (type) {
      case OutlineSectionType.chapter:
        return 14;
      case OutlineSectionType.section:
        return 13;
      case OutlineSectionType.subsection:
        return 12;
    }
  }

  /// 获取章节类型对应的字体粗细
  FontWeight _getSectionFontWeight(OutlineSectionType type) {
    switch (type) {
      case OutlineSectionType.chapter:
        return FontWeight.bold;
      case OutlineSectionType.section:
        return FontWeight.w600;
      case OutlineSectionType.subsection:
        return FontWeight.w500;
    }
  }

  /// 滚动到指定章节
  void _scrollToSection(int index) {
    if (_outlineScrollController.hasClients) {
      // 计算滚动位置（每个章节大约占用的高度）
      final position = index * 200.0;
      _outlineScrollController.animateTo(
        position,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  /// 构建右侧内容区域
  Widget _buildOutlineContent(List<OutlineSection> sections) {
    return SingleChildScrollView(
      controller: _outlineScrollController,
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: sections.asMap().entries.map((entry) {
          final index = entry.key;
          final section = entry.value;
          return _buildSectionContent(section, index);
        }).toList(),
      ),
    );
  }

  /// 构建单个章节内容
  Widget _buildSectionContent(OutlineSection section, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 章节标题
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: _getSectionBackgroundColor(section.type),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  _getSectionIcon(section.type),
                  size: 20,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    section.title,
                    style: TextStyle(
                      fontSize: _getSectionContentFontSize(section.type),
                      fontWeight: _getSectionFontWeight(section.type),
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),
          // 章节内容
          if (section.content.isNotEmpty)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerLowest,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
                ),
              ),
              child: _isMarkdownMode
                  ? MarkdownBody(
                      data: section.content,
                      selectable: true,
                      styleSheet: MarkdownStyleSheet(
                        p: TextStyle(
                          fontSize: 14,
                          height: 1.6,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                        h1: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                        h2: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                        h3: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                    )
                  : SelectableText(
                      section.content,
                      style: TextStyle(
                        fontSize: 14,
                        height: 1.6,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
            ),
        ],
      ),
    );
  }

  /// 获取章节类型对应的背景色
  Color _getSectionBackgroundColor(OutlineSectionType type) {
    switch (type) {
      case OutlineSectionType.chapter:
        return Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3);
      case OutlineSectionType.section:
        return Theme.of(context).colorScheme.secondaryContainer.withValues(alpha: 0.3);
      case OutlineSectionType.subsection:
        return Theme.of(context).colorScheme.tertiaryContainer.withValues(alpha: 0.3);
    }
  }

  /// 获取章节内容对应的字体大小
  double _getSectionContentFontSize(OutlineSectionType type) {
    switch (type) {
      case OutlineSectionType.chapter:
        return 18;
      case OutlineSectionType.section:
        return 16;
      case OutlineSectionType.subsection:
        return 14;
    }
  }

  /// 初始化大纲章节数据
  void _initOutlineChapters(String outlineContent) {
    if (_outlineChapters.isNotEmpty) return; // 已经初始化过了

    // 解析现有的大纲内容
    final lines = outlineContent.split('\n');
    int currentChapterNumber = 0;
    String currentChapterTitle = '';
    StringBuffer currentChapterContent = StringBuffer();

    for (final line in lines) {
      final trimmedLine = line.trim();

      // 检测章节标题
      final chapterMatch = RegExp(r'^第(\d+)章[：:\s]*(.*)$').firstMatch(trimmedLine);
      if (chapterMatch != null) {
        // 保存前一章
        if (currentChapterNumber > 0) {
          _outlineChapters.add(OutlineChapterData(
            chapterNumber: currentChapterNumber,
            title: currentChapterTitle,
            content: currentChapterContent.toString().trim(),
          ));
          currentChapterContent.clear();
        }

        // 开始新章
        currentChapterNumber = int.tryParse(chapterMatch.group(1)!) ?? 0;
        currentChapterTitle = chapterMatch.group(2)?.trim() ?? '';
        continue;
      }

      // 跳过元信息行
      if (trimmedLine.startsWith('小说标题：') ||
          trimmedLine.startsWith('生成时间：') ||
          trimmedLine.startsWith('总章节数：')) {
        continue;
      }

      // 添加内容行
      if (currentChapterNumber > 0 && trimmedLine.isNotEmpty) {
        currentChapterContent.writeln(line);
      }
    }

    // 保存最后一章
    if (currentChapterNumber > 0) {
      _outlineChapters.add(OutlineChapterData(
        chapterNumber: currentChapterNumber,
        title: currentChapterTitle,
        content: currentChapterContent.toString().trim(),
      ));
    }

    // 如果没有找到任何章节，创建一个默认章节
    if (_outlineChapters.isEmpty) {
      _outlineChapters.add(OutlineChapterData(
        chapterNumber: 1,
        title: '第一章',
        content: '',
      ));
    }
  }

  /// 构建结构化大纲编辑器
  Widget _buildStructuredOutlineEditor() {
    return Column(
      children: [
        // 工具栏
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceContainerHighest,
            border: Border(
              bottom: BorderSide(
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
              ),
            ),
          ),
          child: Row(
            children: [
              Text(
                '大纲编辑',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
              const Spacer(),
              ElevatedButton.icon(
                onPressed: _addNewChapter,
                icon: const Icon(Icons.add),
                label: const Text('添加章节'),
              ),
            ],
          ),
        ),
        // 章节列表
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: _outlineChapters.length,
            itemBuilder: (context, index) {
              return _buildChapterEditCard(_outlineChapters[index], index);
            },
          ),
        ),
      ],
    );
  }

  /// 构建章节编辑卡片
  Widget _buildChapterEditCard(OutlineChapterData chapterData, int index) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 章节标题编辑
            Row(
              children: [
                SizedBox(
                  width: 80,
                  child: Text(
                    '第${chapterData.chapterNumber}章',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ),
                Expanded(
                  child: TextField(
                    controller: chapterData.titleController,
                    decoration: const InputDecoration(
                      hintText: '输入章节标题',
                      border: OutlineInputBorder(),
                      contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                IconButton(
                  onPressed: () => _deleteChapter(index),
                  icon: const Icon(Icons.delete),
                  color: Colors.red,
                  tooltip: '删除章节',
                ),
              ],
            ),
            const SizedBox(height: 12),
            // 章节内容编辑
            TextField(
              controller: chapterData.contentController,
              maxLines: 6,
              decoration: const InputDecoration(
                hintText: '输入章节大纲内容...',
                border: OutlineInputBorder(),
                alignLabelWithHint: true,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 添加新章节
  void _addNewChapter() {
    setState(() {
      final newChapterNumber = _outlineChapters.isNotEmpty
        ? _outlineChapters.last.chapterNumber + 1
        : 1;

      _outlineChapters.add(OutlineChapterData(
        chapterNumber: newChapterNumber,
        title: '',
        content: '',
      ));
    });
  }

  /// 删除章节
  void _deleteChapter(int index) {
    if (_outlineChapters.length <= 1) {
      Get.snackbar('提示', '至少需要保留一个章节');
      return;
    }

    setState(() {
      _outlineChapters[index].dispose();
      _outlineChapters.removeAt(index);

      // 重新编号
      for (int i = 0; i < _outlineChapters.length; i++) {
        _outlineChapters[i] = OutlineChapterData(
          chapterNumber: i + 1,
          title: _outlineChapters[i].title,
          content: _outlineChapters[i].content,
        );
      }
    });
  }

  /// 创建新章节
  Future<void> _createNewChapter() async {
    final titleController = TextEditingController();

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('创建新章节'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('章节标题'),
            const SizedBox(height: 8),
            TextField(
              controller: titleController,
              decoration: const InputDecoration(
                hintText: '请输入章节标题（可选）',
                border: OutlineInputBorder(),
              ),
              autofocus: true,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('创建'),
          ),
        ],
      ),
    );

    if (result == true) {
      try {
        await _controller.createBlankChapter(
          novelTitle: _currentNovel.title,
          chapterTitle: titleController.text.trim().isEmpty
              ? null
              : titleController.text.trim(),
        );

        // 刷新当前小说数据
        final updatedNovel = _controller.novels.firstWhere(
          (n) => n.title == _currentNovel.title,
        );

        setState(() {
          _currentNovel = updatedNovel.copyWith();
          _initChapterControllers();
          // 切换到新创建的章节
          final newChapterIndex = _currentNovel.chapters.length - 1;
          if (newChapterIndex >= 0) {
            _currentChapterIndex = newChapterIndex;
          }
        });
      } catch (e) {
        // 错误已在controller中处理
      }
    }
  }

  /// 生成格式化的大纲内容
  String _generateOutlineContent() {
    final buffer = StringBuffer();

    // 添加元信息
    buffer.writeln('小说标题：${_currentNovel.title}');
    buffer.writeln('生成时间：${DateTime.now().toString().split('.')[0]}');
    buffer.writeln('总章节数：${_outlineChapters.length}');
    buffer.writeln();

    // 添加各章节大纲
    for (final chapterData in _outlineChapters) {
      buffer.writeln('第${chapterData.chapterNumber}章：${chapterData.title}');
      if (chapterData.content.isNotEmpty) {
        buffer.writeln(chapterData.content);
      }
      buffer.writeln();
    }

    return buffer.toString().trim();
  }












  /// 使用半人马写作生成内容
  Future<void> _generateWithCentaur() async {
    // 检查是否至少有写作指令或情节发展其中之一
    final hasPrompt = _centaurPromptController.text.trim().isNotEmpty;
    final hasPlotDevelopment = _plotDevelopmentController.text.trim().isNotEmpty;

    if (!hasPrompt && !hasPlotDevelopment) {
      Get.snackbar('提示', '请至少填写写作指令或后续情节发展其中一项');
      return;
    }

    setState(() {
      _isGeneratingWithCentaur = true;
      // 确保在编辑模式下，这样用户可以看到流式输出
      if (!_isEditing) {
        _isEditing = true;
      }
    });

    try {
      // 确保NovelController的title设置为当前小说的标题
      final novelController = Get.find<NovelController>();
      novelController.title.value = _currentNovel.title;
      print('[半人马写作] 设置小说标题: ${_currentNovel.title}');
      // 构建增强的提示词
      final enhancedPrompt = await _buildEnhancedPrompt();

      // 调试信息：打印选中的参数
      print('[DEBUG] 半人马写作参数:');
      print('- 知识库: ${_selectedKnowledgeIds.length} 个文档');
      print('- 角色卡片: ${_selectedCharacterIds.length} 个角色');
      print('- 上下文章节: ${_selectedContextChapters.length} 个章节');
      print('- 文风包: $_selectedWritingStyleId');
      print('- 允许创建新角色: $_allowCreateNewCharacters');
      print('- 写作指令: ${_centaurPromptController.text.trim().isNotEmpty ? "已填写" : "未填写"}');
      print('- 情节发展: ${_plotDevelopmentController.text.trim().isNotEmpty ? "已填写" : "未填写"}');
      print('[DEBUG] 完整提示词:\n$enhancedPrompt');

      // 获取当前章节内容
      final currentChapter = _currentNovel.chapters[_currentChapterIndex];
      final currentContent = _chapterControllers[_currentChapterIndex].text;

      // 检查是否需要生成新章节大纲
      bool shouldGenerateOutline = false;
      String? chapterOutlineSource;

      if (currentChapter.number > 0) {
        // 检查大纲中是否已有该章节
        final outlineChapter = _currentNovel.chapters.firstWhereOrNull(
          (c) => c.title == '大纲',
        );

        if (outlineChapter != null) {
          // 解析大纲，查找当前章节
          final outlineContent = outlineChapter.content;
          final chapterPattern = RegExp(r'第' + currentChapter.number.toString() + r'章[：:](.*?)(?=第\d+章|$)', dotAll: true);
          final match = chapterPattern.firstMatch(outlineContent);

          if (match == null || match.group(1)?.trim().isEmpty == true) {
            // 大纲中没有该章节，需要生成大纲
            shouldGenerateOutline = true;

            // 优先使用用户输入的情节发展，否则使用生成的内容
            final plotDevelopment = _plotDevelopmentController.text.trim();
            if (plotDevelopment.isNotEmpty) {
              chapterOutlineSource = plotDevelopment;
            } else {
              // 如果用户没有输入情节发展，我们将在生成内容后使用生成的内容来创建大纲
              chapterOutlineSource = null;
            }
          }
        } else {
          // 没有大纲章节，为新章节生成大纲
          shouldGenerateOutline = true;
          final plotDevelopment = _plotDevelopmentController.text.trim();
          chapterOutlineSource = plotDevelopment.isNotEmpty ? plotDevelopment : null;
        }
      }

      // 生成内容 - 流式输出到文本框
      final buffer = StringBuffer();

      // 清空当前章节内容，准备接收新内容
      setState(() {
        _chapterControllers[_currentChapterIndex].text = '';
      });

      await for (final chunk in _aiService.generateTextStream(
        systemPrompt: _buildSystemPrompt(),
        userPrompt: enhancedPrompt,
        temperature: 0.7,
        maxTokens: 4000,
      )) {
        buffer.write(chunk);

        // 实时更新文本框内容
        setState(() {
          _chapterControllers[_currentChapterIndex].text = buffer.toString();
        });

        // 自动滚动到底部，让用户看到最新生成的内容
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (_chapterTextScrollController.hasClients) {
            _chapterTextScrollController.animateTo(
              _chapterTextScrollController.position.maxScrollExtent,
              duration: const Duration(milliseconds: 100),
              curve: Curves.easeOut,
            );
          }
        });

        // 让UI有时间更新
        await Future.delayed(const Duration(milliseconds: 50));
      }

      final generatedContent = buffer.toString().trim();

      // 最终更新章节内容并保存历史
      setState(() {
        _chapterControllers[_currentChapterIndex].text = generatedContent;
        _saveCurrentToHistory();
      });

      // 如果需要生成大纲，自动生成并保存
      if (shouldGenerateOutline) {
        // 如果有预设的大纲来源（用户输入的情节发展），使用它；否则使用生成的内容
        final outlineSource = chapterOutlineSource ?? generatedContent;
        await _generateAndSaveChapterOutline(currentChapter.number, outlineSource);
      }

      // 使用WidgetsBinding确保在下一帧显示snackbar，避免GetX错误
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Get.snackbar('成功', '内容生成完成');
      });

    } catch (e) {
      print('[ERROR] 半人马写作生成失败: $e');
      // 使用WidgetsBinding确保在下一帧显示snackbar，避免GetX错误
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Get.snackbar('错误', '生成失败: ${e.toString()}');
      });
    } finally {
      setState(() {
        _isGeneratingWithCentaur = false;
      });
    }
  }

  /// 构建系统提示词
  String _buildSystemPrompt() {
    final buffer = StringBuffer();
    buffer.writeln('你是一个专业的小说创作助手，请根据用户的要求提供高质量的内容。');
    buffer.writeln('请用非常简洁的描述方式描述剧情，冲突部分可以详细描写，快节奏，多对话形式，以小见大。');
    buffer.writeln('人物对话格式："xxxxx"某某说道。');
    buffer.writeln('严禁使用任何形式的小标题、序号或章节编号。');
    buffer.writeln('严禁使用情节点、转折点、高潮点等标题或分段标记。');
    buffer.writeln('严禁使用总结性语言，如"总之"、"总的来说"、"简而言之"等。');
    buffer.writeln('严禁添加旁白或解说，严禁添加"作者注"、"编者按"等内容。');
    buffer.writeln('直接用流畅的叙述展开故事，只关注推动情节发展的内容。');
    buffer.writeln();

    // 重要指令
    buffer.writeln('重要指令：');
    buffer.writeln('1. 严格按照用户提供的知识库、角色卡片、文风包等参数进行创作');
    buffer.writeln('2. 如果用户提供了角色卡片，必须严格按照角色设定来描写角色的性格、外貌和行为');
    buffer.writeln('3. 如果用户提供了文风包，必须严格按照指定的文风来写作');
    buffer.writeln('4. 如果用户提供了知识库内容，必须参考其中的设定和背景信息');
    buffer.writeln('5. 如果用户提供了上下文章节，必须保持与前文的连贯性和一致性');
    buffer.writeln('6. 如果用户提供了后续情节发展，必须严格按照指定的情节方向进行创作');

    if (!_allowCreateNewCharacters) {
      buffer.writeln('7. 严格使用用户提供的角色卡片中的角色，不要创造新角色。');
    } else {
      buffer.writeln('7. 可以创造新角色，但要与现有角色保持一致的风格。');
    }

    return buffer.toString();
  }

  /// 构建增强的提示词
  Future<String> _buildEnhancedPrompt() async {
    final buffer = StringBuffer();

    // 添加基础信息
    buffer.writeln('小说标题：${_currentNovel.title}');
    buffer.writeln('小说类型：${_currentNovel.genre}');

    final currentChapter = _currentNovel.chapters[_currentChapterIndex];
    buffer.writeln('当前章节：第${currentChapter.number}章 - ${currentChapter.title}');
    buffer.writeln();

    // 添加参数使用提醒
    buffer.writeln('【重要】请严格按照以下提供的所有参数进行创作，不要忽略任何一个参数：');
    buffer.writeln();

    // 添加知识库内容
    if (_selectedKnowledgeIds.isNotEmpty) {
      try {
        final knowledgeController = Get.find<KnowledgeBaseController>();
        buffer.writeln('=== 知识库参考（必须严格遵循） ===');
        for (final docId in _selectedKnowledgeIds) {
          final doc = knowledgeController.documents.firstWhereOrNull(
            (d) => d.id == docId,
          );
          if (doc != null) {
            buffer.writeln('【${doc.title}】');
            buffer.writeln(doc.content);
            buffer.writeln();
          }
        }
      } catch (e) {
        print('[WARNING] 获取知识库内容失败: $e');
      }
    }

    // 添加文风包
    if (_selectedWritingStyleId != null) {
      try {
        final styleController = Get.find<WritingStylePackageController>();
        final package = styleController.packages.firstWhereOrNull(
          (p) => p.id == _selectedWritingStyleId,
        );
        if (package != null) {
          buffer.writeln('=== 文风参考（必须严格按照此文风写作） ===');
          buffer.writeln(package.getPrompt());
          buffer.writeln();
        }
      } catch (e) {
        print('[WARNING] 获取文风包失败: $e');
      }
    }

    // 添加角色信息
    if (_selectedCharacterIds.isNotEmpty) {
      try {
        final characterService = Get.find<CharacterCardService>();
        buffer.writeln('=== 角色信息（必须严格按照角色设定描写） ===');
        for (final characterId in _selectedCharacterIds) {
          final character = characterService.getCardById(characterId);
          if (character != null) {
            buffer.writeln('【${character.name}】');
            buffer.writeln('性格：${character.personalityTraits}');
            buffer.writeln('背景：${character.background}');
            if (character.appearance.isNotEmpty) {
              buffer.writeln('外貌：${character.appearance}');
            }
            buffer.writeln();
          }
        }
      } catch (e) {
        print('[WARNING] 获取角色信息失败: $e');
      }
    }

    // 添加上下文章节
    if (_selectedContextChapters.isNotEmpty) {
      buffer.writeln('=== 上下文参考（必须保持连贯性和一致性） ===');
      for (final chapterNumber in _selectedContextChapters) {
        final chapter = _currentNovel.chapters.firstWhereOrNull(
          (c) => c.number == chapterNumber,
        );
        if (chapter != null) {
          buffer.writeln('【第${chapter.number}章：${chapter.title}】');
          // 只提供章节摘要，避免上下文过长
          final content = chapter.content;
          if (content.length > 500) {
            buffer.writeln('${content.substring(0, 500)}...');
          } else {
            buffer.writeln(content);
          }
          buffer.writeln();
        }
      }
    }

    // 添加当前章节内容（如果有）
    final currentContent = _chapterControllers[_currentChapterIndex].text;
    if (currentContent.isNotEmpty) {
      buffer.writeln('=== 当前章节内容（需要基于此内容进行修改或续写） ===');
      buffer.writeln(currentContent);
      buffer.writeln();
    }

    // 添加用户指令（如果有）
    final userPrompt = _centaurPromptController.text.trim();
    if (userPrompt.isNotEmpty) {
      buffer.writeln('=== 写作指令（必须严格执行） ===');
      buffer.writeln(userPrompt);
      buffer.writeln();
    }

    // 添加情节发展（如果有）
    final plotDevelopment = _plotDevelopmentController.text.trim();
    if (plotDevelopment.isNotEmpty) {
      buffer.writeln('=== 后续情节发展（必须严格按照此方向发展） ===');
      buffer.writeln(plotDevelopment);
    }

    return buffer.toString();
  }

  /// 显示章节情节发展对话框
  Future<String?> _showChapterOutlineDialog() async {
    final controller = TextEditingController();

    return await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('该章情节发展'),
        content: SizedBox(
          width: double.maxFinite,
          child: TextField(
            controller: controller,
            maxLines: 5,
            decoration: const InputDecoration(
              hintText: '请描述本章的主要情节发展...',
              border: OutlineInputBorder(),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              if (controller.text.trim().isNotEmpty) {
                Navigator.of(context).pop(controller.text.trim());
              }
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  /// 生成并保存章节大纲
  Future<void> _generateAndSaveChapterOutline(int chapterNumber, String content) async {
    try {
      print('[DEBUG] 开始为第${chapterNumber}章生成大纲');
      print('[DEBUG] 章节内容长度: ${content.length}');
      print('[DEBUG] 章节内容前200字: ${content.length > 200 ? content.substring(0, 200) : content}');

      // 检查内容是否为空
      if (content.trim().isEmpty) {
        print('[WARNING] 章节内容为空，无法生成大纲');
        return;
      }

      // 生成章节大纲
      final outlinePrompt = '''
请根据以下章节内容，生成一个简洁的章节大纲（50-100字）：

章节内容：
$content

要求：
1. 概括主要情节发展
2. 突出关键冲突和转折
3. 简洁明了，不超过100字
4. 不要包含章节标题
''';

      final buffer = StringBuffer();
      print('[DEBUG] 开始生成章节大纲...');
      print('[DEBUG] 大纲生成提示词: $outlinePrompt');

      await for (final chunk in _aiService.generateTextStream(
        systemPrompt: '你是一个专业的小说编辑，擅长提炼章节要点。',
        userPrompt: outlinePrompt,
        temperature: 0.3,
        maxTokens: 200,
      )) {
        buffer.write(chunk);
        print('[DEBUG] 收到大纲生成块: $chunk');
      }

      final generatedOutline = buffer.toString().trim();
      print('[DEBUG] 生成的大纲内容: "$generatedOutline"');
      print('[DEBUG] 大纲内容长度: ${generatedOutline.length}');

      // 如果生成的大纲为空，使用默认大纲
      final finalOutline = generatedOutline.isEmpty
          ? '本章节内容概要（AI生成失败，请手动编辑）'
          : generatedOutline;
      print('[DEBUG] 最终大纲内容: "$finalOutline"');

      // 更新或创建大纲章节
      final outlineChapterIndex = _currentNovel.chapters.indexWhere((c) => c.title == '大纲');

      String newOutlineContent;
      List<Chapter> updatedChapters;

      if (outlineChapterIndex != -1) {
        // 已有大纲章节，更新它
        final outlineChapter = _currentNovel.chapters[outlineChapterIndex];
        final currentOutline = outlineChapter.content;

        // 添加新章节大纲
        newOutlineContent = currentOutline.isEmpty
            ? '第${chapterNumber}章：${finalOutline}'
            : '$currentOutline\n\n第${chapterNumber}章：${finalOutline}';

        // 创建更新的章节
        final updatedChapter = Chapter(
          number: 0,
          title: '大纲',
          content: newOutlineContent,
        );

        // 更新小说
        updatedChapters = List<Chapter>.from(_currentNovel.chapters);
        updatedChapters[outlineChapterIndex] = updatedChapter;
      } else {
        // 没有大纲章节，创建一个新的
        newOutlineContent = '第${chapterNumber}章：${finalOutline}';

        final newOutlineChapter = Chapter(
          number: 0,
          title: '大纲',
          content: newOutlineContent,
        );

        // 将大纲章节插入到第一个位置
        updatedChapters = [newOutlineChapter, ..._currentNovel.chapters];
      }

      final updatedNovel = _currentNovel.copyWith(
        chapters: updatedChapters,
        updatedAt: DateTime.now(),
      );

      // 保存到数据库
      await _controller.saveNovel(updatedNovel);

      // 更新本地状态
      _updateNovel(updatedNovel);

      // 如果添加了新的大纲章节，需要为它创建控制器
      if (outlineChapterIndex == -1) {
        print('[DEBUG] 添加新的大纲章节控制器');
        print('[DEBUG] 当前章节索引: $_currentChapterIndex');
        print('[DEBUG] 控制器数量: ${_chapterControllers.length}');

        // 只为新添加的大纲章节创建控制器，不影响其他章节
        final outlineController = TextEditingController(text: newOutlineContent);
        _chapterControllers.insert(0, outlineController);

        // 调整当前章节索引，因为插入了新的大纲章节
        if (_currentChapterIndex >= 0) {
          _currentChapterIndex += 1;
          print('[DEBUG] 调整后的章节索引: $_currentChapterIndex');
        }
      } else {
        print('[DEBUG] 更新现有大纲章节控制器');
        // 更新现有大纲章节的控制器
        _chapterControllers[outlineChapterIndex].text = newOutlineContent;
      }

      print('[DEBUG] 当前章节内容长度: ${_chapterControllers[_currentChapterIndex].text.length}');

      print('[INFO] 章节大纲已自动生成并保存');

      // 根据大纲内容是否为默认内容来显示不同的提示
      // 使用WidgetsBinding确保在下一帧显示snackbar，避免GetX错误
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (finalOutline.contains('AI生成失败')) {
          Get.snackbar('警告', '第${chapterNumber}章大纲生成失败，已添加默认大纲，请手动编辑');
        } else {
          Get.snackbar('成功', '第${chapterNumber}章大纲已自动生成并添加到大纲中');
        }
      });
    } catch (e) {
      print('[ERROR] 生成章节大纲失败: $e');
      // 不显示错误给用户，因为这是后台操作
    }
  }


}

class NovelDetailController extends GetxController {
  final Novel novel;
  final RxList<int> selectedChapters = <int>[].obs;
  final RxBool isReviewing = false.obs;
  final RxBool isGenerating = false.obs;
  final RxBool isPaused = false.obs;
  final RxInt currentProcessingChapter = 0.obs;
  final reviewRequirementsController = TextEditingController();
  final _contentReviewService = Get.find<ContentReviewService>();
  final _aiService = Get.find<AIService>();

  NovelDetailController(this.novel);

  @override
  void onInit() {
    super.onInit();
    checkForUnfinishedTask();
  }

  @override
  void onClose() {
    reviewRequirementsController.dispose();
    super.onClose();
  }

  void updateGeneratingStatus(bool value) {
    isGenerating.value = value;
  }

  void updatePausedStatus(bool value) {
    isPaused.value = value;
  }

  Future<void> checkForUnfinishedTask() async {
    final prefs = await SharedPreferences.getInstance();
    final savedTask = prefs.getString('unfinished_task_${novel.id}');

    if (savedTask != null) {
      final taskData = json.decode(savedTask);
      selectedChapters.value = List<int>.from(taskData['selected_chapters']);
      currentProcessingChapter.value = taskData['current_chapter'];

      if (selectedChapters.isNotEmpty) {
        Get.dialog(
          AlertDialog(
            title: const Text('发现未完成的任务'),
            content: const Text('是否继续上次未完成的润色任务？'),
            actions: [
              TextButton(
                onPressed: () {
                  Get.back();
                  clearUnfinishedTask();
                },
                child: const Text('取消'),
              ),
              ElevatedButton(
                onPressed: () {
                  Get.back();
                  resumeUnfinishedTask(taskData['requirements']);
                },
                child: const Text('继续'),
              ),
            ],
          ),
        );
      }
    }
  }

  Future<void> clearUnfinishedTask() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('unfinished_task_${novel.id}');
    selectedChapters.clear();
    currentProcessingChapter.value = 0;
    isPaused.value = false;
    isGenerating.value = false;
  }

  Future<void> saveCurrentProgress() async {
    if (!isGenerating.value) return;

    final prefs = await SharedPreferences.getInstance();
    final taskData = {
      'selected_chapters': selectedChapters.toList(),
      'current_chapter': currentProcessingChapter.value,
      'requirements': reviewRequirementsController.text,
    };
    await prefs.setString('unfinished_task_${novel.id}', json.encode(taskData));
  }

  void pauseGeneration() {
    isPaused.value = true;
    saveCurrentProgress();
  }

  void resumeGeneration() {
    isPaused.value = false;
    continueGeneration();
  }

  Future<void> resumeUnfinishedTask(String requirements) async {
    reviewRequirementsController.text = requirements;
    isGenerating.value = true;
    await continueGeneration();
  }

  Future<void> continueGeneration() async {
    if (!isGenerating.value) return;

    try {
      for (int i = currentProcessingChapter.value;
          i < selectedChapters.length;
          i++) {
        if (isPaused.value) {
          await saveCurrentProgress();
          return;
        }

        currentProcessingChapter.value = i;
        final chapterIndex = selectedChapters[i];
        final chapter = novel.chapters[chapterIndex];

        final reviewedContent = await _contentReviewService.reviewContent(
          content: chapter.content,
          style: '与原文风格一致',
          model: AIModel.values.firstWhere(
            (m) =>
                m.toString().split('.').last ==
                Get.find<ApiConfigController>().selectedModelId.value,
            orElse: () => AIModel.deepseek,
          ),
        );

        novel.chapters[chapterIndex] =
            chapter.copyWith(content: reviewedContent);
        await saveCurrentProgress();
      }

      Get.snackbar('成功', '章节润色完成');
      await clearUnfinishedTask();
    } catch (e) {
      Get.snackbar('错误', '章节润色失败：$e');
      isPaused.value = true;
      await saveCurrentProgress();
    }
  }

  Future<void> reviewSelectedChapters() async {
    try {
      isGenerating.value = true;
      isPaused.value = false;
      currentProcessingChapter.value = 0;
      Get.back(); // 关闭对话框

      await continueGeneration();
    } catch (e) {
      Get.snackbar('错误', '章节润色失败：$e');
    }
  }

  void showReviewDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('章节润色'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Obx(() => Text('已选择 ${selectedChapters.length} 个章节')),
            const SizedBox(height: 16),
            TextField(
              controller: reviewRequirementsController,
              decoration: const InputDecoration(
                labelText: '润色要求（可选）',
                hintText: '请输入具体的润色要求...',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: reviewSelectedChapters,
            child: const Text('开始润色'),
          ),
        ],
      ),
    );
  }

  Future<void> generateContent() async {
    if (isGenerating.value) return;

    try {
      updateGeneratingStatus(true);

      await for (final _ in _aiService.generateTextStream(
        systemPrompt: '''作为一个专业的小说创作助手，请遵循以下创作原则：

1. 故事逻辑：
   - 确保因果关系清晰合理，事件发展有其必然性
   - 人物行为要符合其性格特征和处境
   - 情节转折要有铺垫，避免突兀
   - 矛盾冲突的解决要符合逻辑
   - 故事背景要前后一致，细节要互相呼应

2. 叙事结构：
   - 采用灵活多变的叙事手法，避免单一直线式发展
   - 合理安排伏笔和悬念，让故事更有层次感
   - 注意时间线的合理性，避免前后矛盾
   - 场景转换要流畅自然，不生硬突兀
   - 故事节奏要有张弛，紧凑处突出戏剧性

3. 人物塑造：
   - 赋予角色丰富的心理活动和独特性格
   - 人物成长要符合其经历和环境
   - 人物关系要复杂立体，互动要自然
   - 对话要体现人物性格和身份特点
   - 避免脸谱化和类型化的人物描写

4. 环境描写：
   - 场景描写要与情节和人物情感相呼应
   - 细节要生动传神，突出关键特征
   - 环境氛围要配合故事发展
   - 感官描写要丰富多样
   - 避免无关的环境描写，保持紧凑

5. 语言表达：
   - 用词准确生动，避免重复和陈词滥调
   - 句式灵活多样，富有韵律感
   - 善用修辞手法，但不过分堆砌
   - 对话要自然流畅，符合说话人特点
   - 描写要细腻传神，避免空洞''',
        userPrompt: reviewRequirementsController.text,
        maxTokens: 7000,
        temperature: 0.7,
      )) {
        // 这里可以处理每个生成的文本块
      }

      // 生成完成后的处理
    } catch (e) {
      Get.snackbar('错误', '生成失败：$e');
    } finally {
      updateGeneratingStatus(false);
    }
  }
}

class ChapterDetailScreen extends StatelessWidget {
  final Chapter chapter;

  const ChapterDetailScreen({
    super.key,
    required this.chapter,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('第${chapter.number}章：${chapter.title}'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Text(
          chapter.content,
          style: const TextStyle(fontSize: 16.0, height: 1.6),
        ),
      ),
    );
  }
}

/// 大纲章节数据类
class OutlineSection {
  final String id;
  final String title;
  final OutlineSectionType type;
  String content;

  OutlineSection({
    required this.id,
    required this.title,
    required this.type,
    required this.content,
  });
}

/// 大纲章节类型枚举
enum OutlineSectionType {
  chapter,    // 章节
  section,    // 主要部分
  subsection, // 子部分
}

/// 大纲章节数据类（用于结构化编辑）
class OutlineChapterData {
  final int chapterNumber;
  final TextEditingController titleController;
  final TextEditingController contentController;

  OutlineChapterData({
    required this.chapterNumber,
    required String title,
    required String content,
  }) : titleController = TextEditingController(text: title),
       contentController = TextEditingController(text: content);

  void dispose() {
    titleController.dispose();
    contentController.dispose();
  }

  String get title => titleController.text;
  String get content => contentController.text;
}
